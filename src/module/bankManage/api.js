// 加载对应的装饰器
import { Parameters } from '@u/decorator';

class api {
	/**
	 * 根据ID查询银行基本信息
	 * @param {Object} param
	 * - bcBankId {Number} 银行ID 
	 */
	@Parameters(["bcBankId"])
	queryBankById() {
		return this.services.initGet({
			reqUrl: '/bcBank/query',
			param: this.param,
		});
	}

	/**
	 * 新增银行基本信息
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	bcBankAdd() {
		return this.services.initPost({
			reqUrl: '/bcBank/add',
			param: this.param,
		});
	}

	/**
	 * 修改银行基本信息
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	bcBankEdit() {
		return this.services.initPost({
			reqUrl: '/bcBank/edit',
			param: this.param,
		});
	}

	/**
	 * 删除银行基本信息
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	bcBankDeletes() {
		return this.services.initPost({
			reqUrl: '/bcBank/deletes',
			param: this.param,
		});
    }
    
    /**
	 * 导出银行基本信息
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	bcBankExport() {
		return this.services.initGet({
			reqUrl: '/bcBank/export',
			param: this.param,
		});
	}

	/**
	 * 批量更新银证鉴权信息
	 * @param {Object} param
	 * - bankIds {String} 银行ID列表，逗号分隔
	 * - supportSecAuthFlag {String} 支持银证鉴权标识
	 */
	@Parameters(['_data'])
	batchUpdateSecAuth() {
		return this.services.initPost({
			reqUrl: '/bcBank/batchUpdate',
			param: this.param,
		});
	}
}

export default new api();
