// 加载对应的装饰器
import { Parameters } from '@u/decorator';

class api {
	/**
	 * 根据ID查询银行基本信息
	 * @param {Object} param
	 * - bcBankId {Number} 银行ID 
	 */
	@Parameters(["bcBankId"])
	queryBankById() {
		return this.services.initGet({
			reqUrl: '/bcBank/query',
			param: this.param,
		});
	}

	/**
	 * 新增银行基本信息
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	bcBankAdd() {
		return this.services.initPost({
			reqUrl: '/bcBank/add',
			param: this.param,
		});
	}

	/**
	 * 修改银行基本信息
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	bcBankEdit() {
		return this.services.initPost({
			reqUrl: '/bcBank/edit',
			param: this.param,
		});
	}

	/**
	 * 删除银行基本信息
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	bcBankDeletes() {
		return this.services.initPost({
			reqUrl: '/bcBank/deletes',
			param: this.param,
		});
    }
    
    /**
	 * 导出银行基本信息
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	bcBankExport() {
		return this.services.initGet({
			reqUrl: '/bcBank/export',
			param: this.param,
		});
	}

	/**
	 * 查询银证鉴权信息
	 * @param {Object} param
	 * - bankId {String} 银行ID
	 */
	@Parameters(["bankId"])
	querySecAuth() {
		return this.services.initGet({
			reqUrl: '/bcBank/querySecAuth',
			param: this.param,
		});
	}

	/**
	 * 更新银证鉴权信息
	 * @param {Object} param
	 */
	@Parameters(['_data'])
	updateSecAuth() {
		return this.services.initPost({
			reqUrl: '/bcBank/updateSecAuth',
			param: this.param,
		});
	}
}

export default new api();
