<template>
  <div>
    <a-modal :title="(requestId ? '编辑' : '添加') + '银行信息'" :visible="showPop" :destroyOnClose="true" @ok="getValue"
      @cancel="closePop" class="ant_modal_bigtable">
      <template slot="footer">
        <a-button key="back" @click="resetForm">
          重置
        </a-button>
        <a-button type="primary" key="submit" @click="getValue">
          {{ requestId ? "修改" : "添加" }}
        </a-button>
      </template>
      <a-form-model ref="form" :model="form" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-model-item label="银行简称" prop="bankName">
          <a-input placeholder="请输入银行简称" v-model="form.bankName"></a-input>
        </a-form-model-item>
        <a-form-model-item label="银行全称" prop="bankFullName">
          <a-input placeholder="请输入银行全称" v-model="form.bankFullName"></a-input>
        </a-form-model-item>
        <a-form-model-item label="银行标识" prop="bankNo">
          <a-input placeholder="请输入银行标识" v-model="form.bankNo"></a-input>
        </a-form-model-item>
        <a-form-model-item label="绑定方式" prop="bindType">
          <a-select v-model="form.bindType" placeholder="请选择绑定方式">
            <a-select-option :value="v.key" v-for="v in isBuffetList" :key="v.key">
              {{ v.value }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item 
          label="7*24h转账" 
          prop="support7x24Flag"
          :label-col="{ span: 6 }" 
          :wrapper-col="{ span: 16 }"
          :style="{ whiteSpace: 'nowrap' }">
          <a-checkbox-group 
            v-model="form.support7x24Flag" 
            name="checkboxgroup" 
            @change="handle7x24FlagChange"
            :style="{ display: 'block', marginTop: '8px' }">
            <a-checkbox 
              v-for="item in support7x24FlagList" 
              :key="item.key" 
              :value="item.key" 
              :disabled="isDisabled7x24(item.key)"
              :style="{ marginLeft: 0, marginRight: '16px' }">
              {{ item.value }}
            </a-checkbox>
          </a-checkbox-group>
        </a-form-model-item>
        <a-form-model-item label="需要卡号" prop="needCardNo">
          <a-radio-group name="needCardNo" v-model="form.needCardNo">
            <a-radio value="1" key="1">需要</a-radio>
            <a-radio value="0" key="0">不需要</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="需要密码" prop="needPassword">
          <a-radio-group name="needPassword" v-model="form.needPassword">
            <a-radio value="1" key="1">是</a-radio>
            <a-radio value="0" key="0">否</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="密码类型">
          <a-select v-model="form.passwordType" placeholder="请选择密码类型">
            <a-select-option :value="v.dictValue" v-for="v in passwordTypeList" :key="v.dictValue">
              {{ v.dictLabel }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="支持柜台" prop="counterKind">
          <a-checkbox-group v-model="form.counterKind" name="checkboxgroup">
            <a-checkbox v-for="item in counterKindList" :key="item.key" :value="item.key">
              {{ item.value }}
            </a-checkbox>
          </a-checkbox-group>
        </a-form-model-item>
        <a-form-model-item label="支持证件" prop="idKind">
          <a-checkbox-group class="idKind" v-model="form.idKind" name="checkboxgroup">
            <a-checkbox v-for="item in idKindList" :key="item.dictValue" :value="item.dictValue">
              {{ item.dictLabel }}
            </a-checkbox>
          </a-checkbox-group>
        </a-form-model-item>
        <a-form-model-item label="排序值" prop="orderline">
          <a-input-number id="inputNumber" v-model="form.orderline" :min="0" />
        </a-form-model-item>
        <a-form-model-item label="银行图标logo">
          <a-upload name="file" list-type="picture-card" :multiple="true" :data="updataType" :file-list="PCFileList"
            action="/bc-manage-server/file/upload" @preview="handlePreview($event, 'PC')"
            @change="handleChange($event, 'PC')">
            <div v-if="PCFileList.length < 1">
              <a-icon type="plus" />
              <div class="ant-upload-text">
                logo上传
              </div>
            </div>
          </a-upload>
        </a-form-model-item>
        <a-form-model-item label="缩略图logo">
          <a-upload name="file" list-type="picture-card" :multiple="false" :data="updataType" :file-list="APPFileList"
            action="/bc-manage-server/file/upload" @preview="handlePreview($event, 'APP')"
            @change="handleChange($event, 'APP')">
            <div v-if="APPFileList.length < 1">
              <a-icon type="plus" />
              <div class="ant-upload-text">
                logo上传
              </div>
            </div>
          </a-upload>
        </a-form-model-item>
        <a-form-model-item label="是否启用" prop="state">
          <a-radio-group name="state" v-model="form.state">
            <a-radio value="1" key="1">是</a-radio>
            <a-radio value="0" key="0">否</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="绑卡提示信息" prop="bindTip">
          <a-input placeholder="请输入绑卡提示信息" v-model="form.bindTip"></a-input>
        </a-form-model-item>
        <a-form-model-item label="支持银证鉴权标识" prop="supportSecAuthFlag">
          <a-radio-group name="supportSecAuthFlag" v-model="form.supportSecAuthFlag">
            <a-radio value="0" key="0">不支持</a-radio>
            <a-radio value="1" key="1">支持</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="银行活动内容" prop="activityContent">
          <a-textarea
            placeholder="请输入银行活动内容"
            v-model="form.activityContent"
            :maxLength="100"
            :rows="4"
            show-count>
          </a-textarea>
        </a-form-model-item>
        <a-form-model-item label="银行活动开始时间" prop="activityStartTime">
          <a-date-picker
            v-model="form.activityStartTime"
            show-time
            format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择银行活动开始时间"
            style="width: 100%">
          </a-date-picker>
        </a-form-model-item>
        <a-form-model-item label="银行活动结束时间" prop="activityEndTime">
          <a-date-picker
            v-model="form.activityEndTime"
            show-time
            format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择银行活动结束时间"
            style="width: 100%">
          </a-date-picker>
        </a-form-model-item>
        <a-form-model-item label="绑定激活方式" prop="bindActType">
          <a-select v-model="form.bindActType" placeholder="请选择绑定激活方式">
            <a-select-option value="0" key="0">首笔银行激活</a-select-option>
            <a-select-option value="1" key="1">无需激活</a-select-option>
            <a-select-option value="2" key="2">网银激活</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="银行分组" prop="bankGroup">
          <a-select v-model="form.bankGroup" placeholder="请选择银行分组" allowClear>
            <a-select-option v-for="item in bankGroupList" :key="item.groupNo" :value="item.groupNo">
              {{ item.groupNo }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
      <a-modal :visible="previewVisible" :footer="null" @cancel="previewVisible = false">
        <img alt="example" style="width: 100%" :src="previewImage" />
      </a-modal>
    </a-modal>
  </div>
</template>
<script>
export default {
  name: "",
  inject: ["api"],
  data() {
    return {
      updataType: {
        type: "image",
      },
      PCFileList: [],
      APPFileList: [],
      isBuffetList: this.$baseDict.base.isBuffetList, //方式列表
      passwordTypeList: this.$dict.dictTypeList("bc.common.clientPwdType"),
      counterKindList: [
        { key: "0", value: "普通柜台" },
        { key: "1", value: "信用柜台" },
      ],
      //是否支持7*24小时转账
      support7x24FlagList: [
        { key: "0", value: "不支持" },
        { key: "1", value: "普通柜台支持" },
        { key: "2", value: "信用柜台支持" },
      ],
      // 银行分组列表
      bankGroupList: [
        { groupNo: "1" }, // 工商银行
        { groupNo: "2" }, // 建设银行
        { groupNo: "3" }, // 招商银行
        { groupNo: "5" }, // 兴业银行
        { groupNo: "6" }, // 农业银行
        { groupNo: "7" }, // 民生银行
        { groupNo: "8" }, // 交通银行
        { groupNo: "9" }, // 中信银行
        { groupNo: "10" }, // 中国银行
        { groupNo: "11" }, // 华夏银行
        { groupNo: "12" }, // 上海银行
        { groupNo: "14" }, // 光大银行
        { groupNo: "15" }, // 浦发银行
        { groupNo: "16" }, // 平安银行
        { groupNo: "17" }, // 北京银行
        { groupNo: "18" }, // 江苏银行
        { groupNo: "19" }, // 宁波银行
        { groupNo: "20" }, // 南京银行
        { groupNo: "21" }, // 邮储银行
        { groupNo: "22" }, // 广发银行
        { groupNo: "23" }, // 广州银行
        { groupNo: "24" }, // 工商银行、建设银行、招商银行、中国银行、兴业银行、农业银行、民生银行、交通银行、光大银行、邮储银行、广发银行、华夏银行
      ],
      idKindList: this.$dict.dictTypeList("bc.common.idKind"),
      previewVisible: false,
      previewImage: "",
      form: {
        bankNo: "", //Y
        bankName: "", //Y
        bankFullName: "", //Y
        bankLogo: "",
        bankSimpleLogo: "",
        needCardNo: "1",
        needPassword: "1",
        orderline: "0",
        bindType: undefined,
        bindTip: "",
        passwordType: undefined,
        support7x24Flag: undefined,
        counterKind: ["0", "1"],
        idKind: ["0"],
        state: "1",
        // 银证鉴权相关字段
        supportSecAuthFlag: "0",
        activityContent: "",
        activityStartTime: null,
        activityEndTime: null,
        bindActType: undefined,
        bankGroup: "",
      },
      rules: {
        bankName: [
          {
            required: true,
            message: "银行简称不能为空",
            trigger: "blur",
          },
        ],
        bankFullName: [
          {
            required: true,
            message: "银行全称不能为空",
            trigger: "blur",
          },
        ],
        bankNo: [
          {
            required: true,
            message: "英文标识不能为空",
            trigger: "blur",
          },
        ],
        bindType: [
          {
            required: true,
            message: "绑定方式不能为空",
            trigger: "change",
          },
        ],
        needCardNo: [
          {
            required: true,
            message: "请选择是否需要卡号",
            trigger: "change",
          },
        ],
        needPassword: [
          {
            required: true,
            message: "请选择是否需要密码",
            trigger: "change",
          },
        ],
        counterKind: [
          {
            required: true,
            message: "请选择支持的柜台",
            trigger: "change",
          },
        ],
        idKind: [
          {
            required: true,
            message: "请选择支持的证件",
            trigger: "change",
          },
        ],
        support7x24Flag: [
          {
            required: true,
            message: "请选择是否支持7*24小时转账",
            trigger: "change",
          },
        ],
        bindTip: [
          {
            message: "最多只能输入300个字",
            max: 300
          }
        ],
        supportSecAuthFlag: [
          {
            required: true,
            message: "请选择是否支持银证鉴权",
            trigger: "change",
          },
        ],
        activityContent: [
          {
            max: 100,
            message: "银行活动内容最多100个字符",
            trigger: "blur",
          },
        ]
      },
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    requestId: {
      type: String,
      default: "",
    },
    parameterData: {
      type: Object,
    },
  },
  mounted() { },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val); // visible 改变的时候通知父组件
      },
    },
  },
  methods: {
    updateList() {
      this.closePop();
      this.$emit("success");
    },
    // 重置表单
    resetForm() {
      this.form = {
        bankNo: "", //Y
        bankName: "", //Y
        bankFullName: "", //Y
        bankLogo: "",
        bankSimpleLogo: "",
        needCardNo: "1",
        needPassword: "1",
        orderline: "0",
        bindType: undefined,
        bindTip: "",
        passwordType: undefined,
        support7x24Flag: undefined,
        counterKind: ["0", "1"],
        idKind: ["0"],
        state: "1",
        // 银证鉴权相关字段
        supportSecAuthFlag: "0",
        activityContent: "",
        activityStartTime: null,
        activityEndTime: null,
        bindActType: undefined,
        bankGroup: "",
      };
    },
    //关闭
    closePop() {
      this.resetForm();
      (this.PCFileList = []), (this.APPFileList = []), (this.showPop = false);
    },
    //提交
    getValue() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        let param = JSON.parse(JSON.stringify(this.form));
        param.counterKind = param.counterKind.join(",");
        if(Array.isArray(param.support7x24Flag))
        param.support7x24Flag = param.support7x24Flag.join(",");

        param.idKind = param.idKind.join(",");

        // 格式化时间字段
        if (param.activityStartTime) {
          param.activityStartTime = param.activityStartTime.format('YYYY-MM-DD HH:mm:ss');
        }
        if (param.activityEndTime) {
          param.activityEndTime = param.activityEndTime.format('YYYY-MM-DD HH:mm:ss');
        }
        let callback = ({ code, msg }) => {
          if (code != 0)
            return this.$message.error(
              `银行信息${this.requestId ? "修改" : "添加"}失败：${msg}`
            );
          this.$message.success(
            `银行信息${this.requestId ? "修改" : "添加"}成功！`
          );
          this.updateList();
        };
        this.requestId
          ? this.api.bcBankEdit(param).then(callback)
          : this.api.bcBankAdd(param).then(callback);
      });
    },
    handlePreview(info) {
      if (info.url) {
        this.previewImage = info.url;
        this.previewVisible = true;
      }
      if (info.response && info.response.data) {
        this.previewImage =
          window.$hvue.customConfig.fileUrl + info.response.data;
        this.previewVisible = true;
      }
    },
    // 选择图片修改
    handleChange(info, type) {
      let fileList = [...info.fileList];
      fileList = fileList.slice(-2);
      let resImg = info.file.response && info.file.response.data;
      fileList = fileList.map((file) => {
        if (file.response) {
          file.url = window.$hvue.customConfig.fileUrl + file.response.data;
        }
        return file;
      });
      if (type === "PC") {
        this.PCFileList = fileList;
        this.form.bankLogo = resImg;
      } else if (type === "APP") {
        this.APPFileList = fileList;
        this.form.bankSimpleLogo = resImg;
      }
    },

    getDetail() {
      this.PCFileList = this.parameterData.bankLogo
        ? [
          {
            uid: "1",
            name: "image.png",
            status: "done",
            url:
              window.$hvue.customConfig.fileUrl + this.parameterData.bankLogo,
          },
        ]
        : [];
      this.APPFileList = this.parameterData.bankSimpleLogo
        ? [
          {
            uid: "2",
            name: "image.png",
            status: "done",
            url:
              window.$hvue.customConfig.fileUrl +
              this.parameterData.bankSimpleLogo,
          },
        ]
        : [];
      this.form = {
        ...this.parameterData,
      };
      this.form.counterKind = this.form.counterKind
        ? this.form.counterKind.split(",")
        : ["0", "1"];
      this.form.idKind = this.form.idKind ? this.form.idKind.split(",") : ["0"];

      // 处理时间字段
      if (this.form.activityStartTime) {
        this.form.activityStartTime = this.$moment(this.form.activityStartTime);
      }
      if (this.form.activityEndTime) {
        this.form.activityEndTime = this.$moment(this.form.activityEndTime);
      }
    },
    // 处理7x24h转账复选框变化
    handle7x24FlagChange(checkedValues) {
      // 如果选择了"不支持"，则清空其他选项
      if(checkedValues.includes("0")) {
        this.form.support7x24Flag = ["0"];
      }
      // 如果选择了其他选项，则移除"不支持"选项
      else if(checkedValues.length > 0 && this.form.support7x24Flag && this.form.support7x24Flag.includes("0")) {
        const newValues = [...checkedValues];
        const index = newValues.indexOf("0");
        if(index > -1) {
          newValues.splice(index, 1);
        }
        this.form.support7x24Flag = newValues;
      }
    },
    // 判断7x24h转账复选框是否禁用
    isDisabled7x24(key) {
      // 如果当前key不是"不支持"且已选中"不支持"，则禁用该选项
      return key !== "0" && this.form.support7x24Flag && this.form.support7x24Flag.includes("0");
    },
  },
  watch: {
    isPopShow(n) {
      let _this = this;
      if (n) {
        if (_this.requestId != "") {
          _this.$nextTick(() => {
            _this.getDetail();
          });
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .idKind .ant-checkbox-wrapper {
  display: block;
}

::v-deep .idKind .ant-checkbox-wrapper+.ant-checkbox-wrapper {
  margin-left: 0;
}

::v-deep .long-text-item .ant-form-item-control {
  word-break: break-all;
  word-wrap: break-word;
  white-space: normal;
}
.ant-form-item-label {
  overflow: visible !important;
}
</style>
