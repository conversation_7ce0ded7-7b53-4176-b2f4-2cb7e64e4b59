<template>
  <div>
    <a-modal 
      title="银证鉴权" 
      :visible="showPop" 
      :destroyOnClose="true" 
      @ok="getValue"
      @cancel="closePop" 
      class="ant_modal_bigtable">
      <template slot="footer">
        <a-button key="back" @click="resetForm">
          重置
        </a-button>
        <a-button type="primary" key="submit" @click="getValue">
          保存
        </a-button>
      </template>
      <a-form-model ref="form" :model="form" :rules="rules" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
        <a-form-model-item label="支持银证鉴权标识" prop="supportSecAuthFlag">
          <a-radio-group name="supportSecAuthFlag" v-model="form.supportSecAuthFlag">
            <a-radio value="0" key="0">不支持</a-radio>
            <a-radio value="1" key="1">支持</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="银行活动内容" prop="activityContent">
          <a-textarea 
            placeholder="请输入银行活动内容" 
            v-model="form.activityContent"
            :maxLength="100"
            :rows="4"
            show-count>
          </a-textarea>
        </a-form-model-item>
        <a-form-model-item label="银行活动开始时间" prop="activityStartTime">
          <a-date-picker 
            v-model="form.activityStartTime"
            show-time
            format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择银行活动开始时间"
            style="width: 100%">
          </a-date-picker>
        </a-form-model-item>
        <a-form-model-item label="银行活动结束时间" prop="activityEndTime">
          <a-date-picker 
            v-model="form.activityEndTime"
            show-time
            format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择银行活动结束时间"
            style="width: 100%">
          </a-date-picker>
        </a-form-model-item>
        <a-form-model-item label="绑定激活方式" prop="bindActType">
          <a-select v-model="form.bindActType" placeholder="请选择绑定激活方式">
            <a-select-option value="0" key="0">首笔银行激活</a-select-option>
            <a-select-option value="1" key="1">无需激活</a-select-option>
            <a-select-option value="2" key="2">网银激活</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="银行分组" prop="bankGroup">
          <a-select v-model="form.bankGroup" placeholder="请选择银行分组" allowClear>
            <a-select-option v-for="item in bankGroupList" :key="item.groupNo" :value="item.groupNo">
              {{ item.groupNo }} - {{ item.bankCode }} {{ item.bankName }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
export default {
  name: "secAuth",
  inject: ["api"],
  data() {
    return {
      form: {
        supportSecAuthFlag: "0",
        activityContent: "",
        activityStartTime: null,
        activityEndTime: null,
        bindActType: undefined,
        bankGroup: "",
      },
      bankGroupList: [
        { groupNo: "1", bankCode: "4", bankName: "工商银行" },
        { groupNo: "2", bankCode: "6", bankName: "建设银行" },
        { groupNo: "3", bankCode: "7", bankName: "招商银行" },
        { groupNo: "4", bankCode: "9", bankName: "兴业银行" },
        { groupNo: "5", bankCode: "A", bankName: "农业银行" },
        { groupNo: "6", bankCode: "B", bankName: "民生银行" },
        { groupNo: "7", bankCode: "C", bankName: "交通银行" },
        { groupNo: "8", bankCode: "D", bankName: "中信银行" },
        { groupNo: "9", bankCode: "E", bankName: "中国银行" },
        { groupNo: "10", bankCode: "F", bankName: "华夏银行" },
        { groupNo: "11", bankCode: "G", bankName: "上海银行" },
        { groupNo: "12", bankCode: "I", bankName: "光大银行" },
        { groupNo: "13", bankCode: "J", bankName: "浦发银行" },
        { groupNo: "14", bankCode: "K", bankName: "平安银行" },
        { groupNo: "15", bankCode: "H", bankName: "北京银行" },
        { groupNo: "16", bankCode: "N", bankName: "江苏银行" },
        { groupNo: "17", bankCode: "P", bankName: "宁波银行" },
        { groupNo: "18", bankCode: "O", bankName: "南京银行" },
        { groupNo: "19", bankCode: "Q", bankName: "邮储银行" },
        { groupNo: "20", bankCode: "M", bankName: "广发银行" },
        { groupNo: "21", bankCode: "S", bankName: "广州银行" },
      ],
      rules: {
        supportSecAuthFlag: [
          {
            required: true,
            message: "请选择是否支持银证鉴权",
            trigger: "change",
          },
        ],
        activityContent: [
          {
            max: 100,
            message: "银行活动内容最多100个字符",
            trigger: "blur",
          },
        ],
        activityStartTime: [
          {
            required: false,
            message: "请选择银行活动开始时间",
            trigger: "change",
          },
        ],
        activityEndTime: [
          {
            required: false,
            message: "请选择银行活动结束时间",
            trigger: "change",
          },
        ],
      },
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    bankId: {
      type: String,
      default: "",
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val);
      },
    },
  },
  methods: {
    updateList() {
      this.closePop();
      this.$emit("success");
    },
    // 重置表单
    resetForm() {
      this.form = {
        supportSecAuthFlag: "0",
        activityContent: "",
        activityStartTime: null,
        activityEndTime: null,
        bindActType: undefined,
        bankGroup: "",
      };
    },
    //关闭
    closePop() {
      this.resetForm();
      this.showPop = false;
    },
    //提交
    getValue() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        let param = JSON.parse(JSON.stringify(this.form));
        param.bankId = this.bankId;
        
        // 格式化时间
        if (param.activityStartTime) {
          param.activityStartTime = param.activityStartTime.format('YYYY-MM-DD HH:mm:ss');
        }
        if (param.activityEndTime) {
          param.activityEndTime = param.activityEndTime.format('YYYY-MM-DD HH:mm:ss');
        }

        let callback = ({ code, msg }) => {
          if (code != 0)
            return this.$message.error(`银证鉴权设置失败：${msg}`);
          this.$message.success("银证鉴权设置成功！");
          this.updateList();
        };
        
        this.api.updateSecAuth(param).then(callback);
      });
    },

    getDetail() {
      if (this.bankId) {
        this.api.querySecAuth({ bankId: this.bankId }).then(res => {
          if (res.code === 0 && res.data) {
            this.form = {
              ...res.data,
              activityStartTime: res.data.activityStartTime ? this.$moment(res.data.activityStartTime) : null,
              activityEndTime: res.data.activityEndTime ? this.$moment(res.data.activityEndTime) : null,
            };
          }
        });
      }
    },
  },
  watch: {
    isPopShow(n) {
      if (n) {
        this.getDetail();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
