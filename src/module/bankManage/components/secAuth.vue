<template>
  <div>
    <a-modal 
      title="银证鉴权" 
      :visible="showPop" 
      :destroyOnClose="true" 
      @ok="getValue"
      @cancel="closePop" 
      class="ant_modal_bigtable">
      <template slot="footer">
        <a-button key="back" @click="resetForm">
          重置
        </a-button>
        <a-button type="primary" key="submit" @click="getValue">
          保存
        </a-button>
      </template>
      <a-form-model ref="form" :model="form" :rules="rules" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
        <a-form-model-item label="支持银证鉴权标识" prop="supportSecAuthFlag">
          <a-radio-group name="supportSecAuthFlag" v-model="form.supportSecAuthFlag">
            <a-radio value="0" key="0">不支持</a-radio>
            <a-radio value="1" key="1">支持</a-radio>
          </a-radio-group>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
export default {
  name: "secAuth",
  inject: ["api"],
  data() {
    return {
      form: {
        supportSecAuthFlag: "",
      },
      rules: {
        supportSecAuthFlag: [
          {
            required: true,
            message: "请选择是否支持银证鉴权",
            trigger: "change",
          },
        ],
      },
    };
  },
  props: {
    isPopShow: {
      type: Boolean,
      default: false,
    },
    bankId: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    showPop: {
      get() {
        return this.isPopShow;
      },
      set(val) {
        this.$emit("update:isPopShow", val);
      },
    },
  },
  methods: {
    updateList() {
      this.closePop();
      this.$emit("success");
    },
    // 重置表单
    resetForm() {
      this.form = {
        supportSecAuthFlag: "",
      };
    },
    //关闭
    closePop() {
      this.resetForm();
      this.showPop = false;
    },
    //提交
    getValue() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;

        let param = {
          bankIds: this.bankId.join(","),
          supportSecAuthFlag: this.form.supportSecAuthFlag
        };

        let callback = ({ code, msg }) => {
          if (code != 0)
            return this.$message.error(`银证鉴权设置失败：${msg}`);
          this.$message.success("银证鉴权设置成功！");
          this.updateList();
        };

        this.api.batchUpdateSecAuth(param).then(callback);
      });
    },


  },
  watch: {
    isPopShow(n) {
      if (n) {
        // 弹窗打开时重置表单
        this.resetForm();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
